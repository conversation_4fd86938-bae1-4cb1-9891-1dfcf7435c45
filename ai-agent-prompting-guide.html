<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>如何构建你的AI智能体：11个提升AI智能体的提示词技巧</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --google-blue: #4285F4;
            --google-red: #DB4437;
            --google-yellow: #F4B400;
            --google-green: #0F9D58;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --bg-light: #f8f9fa;
            --bg-dark: #202124;
            --border-color: #dadce0;
            --highlight-bg: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-light);
            padding: 0;
            margin: 0;
            font-size: 18px;
            font-weight: 300;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            overflow: hidden;
        }

        header {
            background-color: var(--google-blue);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -1px;
        }

        h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin: 60px 0 30px;
            color: var(--google-blue);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 40px 0 20px;
            color: var(--text-primary);
        }

        h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 30px 0 15px;
            color: var(--google-red);
        }

        .author {
            font-size: 1.5rem;
            font-weight: 500;
            margin-top: 10px;
        }

        .date {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .content {
            padding: 40px;
            background: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.2rem;
            line-height: 1.7;
        }

        .highlight {
            font-weight: 700;
            color: var(--google-blue);
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--google-red);
        }

        .key-point {
            background-color: var(--highlight-bg);
            border-left: 5px solid var(--google-blue);
            padding: 25px;
            margin: 30px 0;
            border-radius: 4px;
        }

        .key-point p {
            margin-bottom: 0;
            font-weight: 500;
        }

        .section {
            margin-bottom: 60px;
        }

        ul, ol {
            margin: 20px 0 30px 20px;
        }

        li {
            margin-bottom: 15px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .card-body {
            padding: 25px;
        }

        .card-body p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .card-body ul {
            margin-left: 20px;
        }

        .card-body li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer {
            background-color: var(--bg-dark);
            color: white;
            padding: 40px;
            text-align: center;
            font-size: 1rem;
        }

        .quote {
            font-size: 2rem;
            font-weight: 300;
            font-style: italic;
            color: var(--google-blue);
            text-align: center;
            max-width: 800px;
            margin: 60px auto;
            line-height: 1.4;
        }

        .divider {
            height: 4px;
            background: var(--google-blue);
            width: 100px;
            margin: 60px auto;
            border-radius: 2px;
        }

        .emphasis {
            font-weight: 700;
            color: var(--google-red);
            font-size: 1.1em;
        }

        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 3rem;
            }
            h2 {
                font-size: 2.2rem;
            }
            h3 {
                font-size: 1.6rem;
            }
            .content {
                padding: 20px;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>如何构建你的AI智能体</h1>
            <div class="author">Guy Gur-Ari</div>
            <div class="date">Augment Code 联合创始人 | 2025年5月21日</div>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <!-- 引言引用 -->
            <div class="quote">
                "提示词工程已成为现代软件开发中最具杠杆效应的技能之一。你提供给智能体的提示词决定了它如何规划、如何使用工具，以及它是构建还是破坏你的流水线。"
            </div>

            <!-- 关键点 -->
            <div class="key-point">
                <p>微小的改变——一行额外的上下文、一个明确的约束、一个重新排序的指令——往往在准确性和可靠性方面产生 <span class="stat-number">巨大</span> 的收益。</p>
            </div>

            <!-- 第一部分 -->
            <div class="section">
                <h2>什么是提示词工程？</h2>
                
                <p>智能体的<span class="highlight">提示词</span>包括作为输入提供给模型的所有内容。这包括各种组件：</p>
                
                <!-- 卡片网格 -->
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">系统提示词</div>
                        <div class="card-body">
                            <p>包含通用指令，引导模型采用不同的响应风格或自主程度</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">工具定义</div>
                        <div class="card-body">
                            <p>向模型解释在什么情况下应该或不应该使用某个工具</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-yellow); color: black;">工具输出</div>
                        <div class="card-body">
                            <p>告诉模型错误条件和执行结果</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">用户指令</div>
                        <div class="card-body">
                            <p>可以在显示给模型之前重新编写（提示词增强）</p>
                        </div>
                    </div>
                </div>
                
                <p><span class="highlight">提示词工程</span>是通过为模型提供更好的提示词来改善其在任务上表现的艺术。提示词的所有部分都可以通过提示词工程得到改善。</p>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 第二部分 -->
            <div class="section">
                <h2>如何思考模型</h2>
                <p>模型是（人工）智能的。提示模型更像是与人交谈，而不是编程计算机。模型构建的世界观完全基于提示词中的内容。这个视图越完整和一致，模型的结果就越好。</p>
                
                <div class="key-point">
                    <p><span class="emphasis">重要提示：</span>如果模型错误地调用工具，不要在智能体代码中抛出异常。相反，返回一个解释错误的工具结果：<code>工具调用时缺少必需参数xyz</code>。模型会恢复并重试。</p>
                </div>
            </div>

            <!-- 第三部分 -->
            <div class="section">
                <h2>11个提示词工程技巧</h2>
                
                <h3>1. 首先关注上下文</h3>
                <p>提示词工程中最重要的因素是为模型提供<span class="highlight">最佳可能的上下文</span>：用户提供的信息（而不是我们提供的提示词文本）。这是模型用来执行任务的主要信号。</p>
                
                <h3>2. 呈现完整的世界图景</h3>
                <p>通过解释它所操作的环境，帮助模型进入正确的状态，并提供可能有助于其良好表现的细节。</p>
                
                <div class="code-block">
你是一个AI助手，可以访问开发者的代码库。
你可以使用提供的工具读取和写入代码库。
                </div>
                
                <h3>3. 在提示词组件之间保持一致性</h3>
                <p>确保提示词的所有组件（系统提示词、工具定义等）以及底层工具定义都是一致的。</p>
                
                <h3>4. 与用户的视角保持一致</h3>
                <p>考虑用户的视角，并尝试将模型与该视角对齐。</p>
                
                <h3>5. 要彻底</h3>
                <p>模型受益于彻底的提示词。不要担心提示词长度。当前的上下文长度很长，并且会持续增加：通过编写更长的提示词，你无法在提示词预算上产生影响。</p>
                
                <h3>6. 避免过度拟合特定示例</h3>
                <p>模型是强大的模式匹配器，会抓住提示词中的细节。提供具体示例可能是双刃剑。</p>
                
                <h3>7. 考虑工具调用限制</h3>
                <p>工具调用在几个方面受到限制：模型通常会选择正确的工具，但在许多情况下，即使使用最佳提示词，它们也会无法选择正确的工具。</p>
                
                <h3>8. 威胁和唤起同理心有时有效</h3>
                <p>告诉模型诸如"正确执行此操作，否则你将面临财务破产"之类的话有时确实有助于提高性能。</p>
                
                <h3>9. 注意提示词缓存</h3>
                <p>尽可能构建你的提示词，使其在会话期间被追加，以避免使提示词缓存失效。</p>
                
                <h3>10. 模型更关注提示词开头或结尾的信息</h3>
                <p>模型对指令的关注程度似乎是：用户消息 → 输入开头 → 中间某处。如果某些内容很重要，考虑将其添加到用户消息中。</p>
                
                <h3>11. 注意提示词平台期</h3>
                <p>直接提示词能够实现的效果是有限的。提示词工程进入收益递减区域，需要引入其他技术。</p>
            </div>

            <!-- 结束引用 -->
            <div class="quote">
                "掌握提示词工程不在于技巧，而在于有纪律的沟通：给智能体完整、一致的上下文；像验证不可信同事一样验证其行为；并进行经验性迭代。"
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>© 2025 Augment Code | AI智能体开发指南</p>
        </div>
    </div>
</body>
</html>
